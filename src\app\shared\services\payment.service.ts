import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { WindowRefService, ICustomWindow } from './window-ref.service';
import { ProgressService } from './progress.service';
import { AuthService } from './auth.service';
import { PaymentResponseService } from './payment-response.service';
import { LoggerService } from '../interceptors/logger.service';
import { AppConstants } from '../constants/AppConstants';
import { SuccessMessage, ErrorMessage } from '../constants/message.content';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AppConfig } from 'src/app/configs/app.config';
import { BookingRequest } from '../models/booking.model';

// Base booking interface without Razorpay parameters
interface BaseBookingRequest {
  tripType: string;
  duration: string;
  distance: number;
  basicFare: number;
  fixRateNote: string;
  gst: number;
  gstFare: number;
  driverCharge: number;
  couponCode: string;
  couponDiscount: number;
  travelerName: string;
  phoneNumber: string;
  mailId: string;
  paymentMode: number;
  bookingCreatedBy: string;
  paymentOption: number;
  fare: number;
  perKMCharges: string;
  cashAmountToPayDriver: number;
  pickUpCity: string;
  dropOffCity: string;
  carCategory: string;
  carFeatures: string;
  carImage: string;
  carCapacity: number;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpAddressLongLat: string;
  dropOffAddressLongLat: string;
  pickUpDate: string;
  pickUpTime: string;
  tollCharge: number;
}

// Types for PhonePe API
interface PhonePeTokenRequest extends BaseBookingRequest {
  merchantUserId: string;
  callbackUrl: string;
}

interface PhonePeTokenResponse {
  succeeded: boolean;
  message: string;
  data: {
    tokenUrl: string;
    merchantTransactionId: string;
    orderId: string;
  };
}

interface PhonePeVerifyRequest {
  merchantTransactionId: string;
  orderId: string;
}

interface PhonePeVerifyResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: {
    paymentStatus: string;
    transactionId: string;
    orderId: string;
    bookingId: string | null;
    amount: number;
    paymentId: string;
    paymentType: string;
    partialPaymentAmount: number | null;
    remainingAmountForDriver: number | null;
  };
}

declare global {
  interface Window {
    PhonePeCheckout: {
      transact: (options: { tokenUrl: string; callback: (response: string) => void; type: string }) => void;
      closePage: () => void;
    };
  }
}

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private _window: ICustomWindow;
  private currentUser: any;
  private logger: LoggerService;
  private modalContainer: HTMLElement | null = null;

  private readonly PHONEPE_API_BASE = `${AppConfig.CabYaari_WebAPI_New}/api/v1/payments/phonepe`;

  private getHttpOptions() {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      })
    };
  }

  constructor(
    private authService: AuthService,
    private zone: NgZone,
    private toastrService: ToastrService,
    private progressService: ProgressService,
    private windowRef: WindowRefService,
    private paymentService: PaymentResponseService,
    private http: HttpClient,
    private router: Router
  ) {
    this.logger = LoggerService.createLogger('PaymentService');
    this._window = this.windowRef.nativeWindow;
    this.currentUser = this.authService.getCurrentUser();
  }

  initPayment(): void {
    // Create modal container before initiating payment
    this.createModalContainer();
    
    // Get PhonePe token URL from backend
    this.getPhonePeToken().subscribe(
      (tokenResponse) => {
        if (!tokenResponse.succeeded) {
          this.logger.error('Failed to get PhonePe token:', tokenResponse.message);
          this.showPaymentFailedMessage();
          this.removeModalContainer();
          return;
        }

        if (!this._window.PhonePeCheckout) {
          this.logger.error('PhonePe checkout script not loaded');
          this.showPaymentFailedMessage();
          this.removeModalContainer();
          return;
        }

        this.showProgressBar();
        
        // Store merchantTransactionId for verification
        const merchantTransactionId = tokenResponse.data.merchantTransactionId;
        
        // Callback function to handle PhonePe payment response
        const paymentCallback = (response: string) => {
          this.zone.run(() => {
            console.log('PhonePe payment callback received:', response);
            
            // Remove modal container when payment is complete or cancelled
            this.removeModalContainer();
            
            switch (response) {
              case 'USER_CANCEL':
                this.hideProgressBar();
                this.toastrService.info('Payment was cancelled by user');
                break;
                
              case 'CONCLUDED':
                // Verify payment with backend
                this.verifyPayment({
                  merchantTransactionId: merchantTransactionId,
                  orderId: tokenResponse.data.orderId,
                });
                break;
                
              default:
                this.hideProgressBar();
                this.showPaymentFailedMessage();
                break;
            }
          });
        };

        try {
          // Invoke PhonePe payment in iframe mode
          this._window.PhonePeCheckout.transact({
            tokenUrl: tokenResponse.data.tokenUrl,
            callback: paymentCallback,
            type: 'IFRAME'
          });
        } catch (error) {
          this.logger.error('Error initiating PhonePe payment:', error);
          this.hideProgressBar();
          this.showPaymentFailedMessage();
          this.removeModalContainer();
        }
      },
      (error) => {
        this.logger.error('Error getting PhonePe token:', error);
        this.hideProgressBar();
        this.showPaymentFailedMessage();
        this.removeModalContainer();
      }
    );
  }

  private getPhonePeToken(): Observable<PhonePeTokenResponse> {
    // Get the booking request data
    const bookingRequest = this.paymentService.bookingRequestData;
    
    // Create the token request with all booking details (excluding Razorpay parameters)
    const request: PhonePeTokenRequest = {
      // Booking details
      tripType: bookingRequest.tripType,
      duration: bookingRequest.duration,
      distance: bookingRequest.distance,
      basicFare: bookingRequest.basicFare,
      fixRateNote: bookingRequest.fixRateNote,
      gst: bookingRequest.gst,
      gstFare: bookingRequest.gstFare,
      driverCharge: bookingRequest.driverCharge,
      couponCode: bookingRequest.couponCode,
      couponDiscount: bookingRequest.couponDiscount,
      travelerName: bookingRequest.travelerName,
      phoneNumber: bookingRequest.phoneNumber,
      mailId: bookingRequest.mailId,
      paymentMode: bookingRequest.paymentMode,
      bookingCreatedBy: bookingRequest.bookingCreatedBy,
      paymentOption: bookingRequest.paymentOption,
      fare: bookingRequest.fare,
      perKMCharges: bookingRequest.perKMCharges,
      cashAmountToPayDriver: bookingRequest.cashAmountToPayDriver,
      pickUpCity: bookingRequest.pickUpCity,
      dropOffCity: bookingRequest.dropOffCity,
      carCategory: bookingRequest.carCategory,
      carFeatures: bookingRequest.carFeatures,
      carImage: bookingRequest.carImage,
      carCapacity: bookingRequest.carCapacity,
      pickUpAddress: bookingRequest.pickUpAddress,
      dropOffAddress: bookingRequest.dropOffAddress,
      pickUpAddressLongLat: bookingRequest.pickUpAddressLongLat,
      dropOffAddressLongLat: bookingRequest.dropOffAddressLongLat,
      pickUpDate: bookingRequest.pickUpDate,
      pickUpTime: bookingRequest.pickUpTime,
      tollCharge: bookingRequest.tollCharge,

      merchantUserId: this.currentUser.id,
      callbackUrl: `${window.location.origin}/payment-callback`
    };

    this.logger.debug('Getting PhonePe token with request:', request);
    return this.http.post<PhonePeTokenResponse>(
      `${this.PHONEPE_API_BASE}/token`, 
      request, 
      this.getHttpOptions()
    );
  }

  private verifyPayment(verifyRequest: PhonePeVerifyRequest): void {
    this.logger.debug('Verifying payment with request:', verifyRequest);
    this.http.post<PhonePeVerifyResponse>(
      `${this.PHONEPE_API_BASE}/verify`, 
      verifyRequest,
      this.getHttpOptions()
    ).subscribe(
      (response) => {
        this.hideProgressBar();
        this.logger.debug('Payment verification response:', response);

        if (!response.succeeded || (response.data.paymentStatus !== 'COMPLETED' && response.data.paymentStatus !== 'Paid')) {
          this.logger.error('Payment verification failed:', response.message);
          this.logger.error('Payment status received:', response.data?.paymentStatus);
          this.showPaymentFailedMessage();
          return;
        }

        // Payment verification successful - booking is already updated by the verify API
        this.showPaymentSuccessMessage();
        this.paymentService.paymentResponseValue = {
          data: {
            transactionId: response.data.transactionId,
            bookingId: response.data.bookingId,
            paymentStatus: response.data.paymentStatus,
            paymentType: response.data.paymentType,
            partialPaymentAmount: response.data.partialPaymentAmount,
            remainingAmountForDriver: response.data.remainingAmountForDriver
          },
          errors: response.errors || '',
          message: response.message,
          succeeded: response.succeeded
        };

        // Handle navigation - check if bookingId is available
        if (response.data.bookingId) {
          const bookingId = response.data.bookingId;
          this.router.navigate(['userprofile/booking-receipt/' + bookingId]);
        } else {
          // If no bookingId, show success message and navigate to home or bookings page
          this.logger.warn('Payment successful but no bookingId received');
          this.router.navigate(['userprofile/bookings']);
        }
      },
      (error) => {
        this.hideProgressBar();
        this.logger.error('Error verifying payment:', error);
        this.showPaymentFailedMessage();
      }
    );
  }

  private createModalContainer(): void {
    // Remove any existing modal container
    this.removeModalContainer();

    // Create new modal container
    this.modalContainer = document.createElement('div');
    this.modalContainer.id = 'phonepe-payment-modal';
    this.modalContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    `;

    // Add modal content container
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: relative;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow: auto;
    `;

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      border: none;
      background: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 5px;
      line-height: 1;
    `;
    closeButton.onclick = () => {
      this.removeModalContainer();
      this._window.PhonePeCheckout.closePage();
      this.toastrService.info('Payment was cancelled by user');
    };

    modalContent.appendChild(closeButton);
    this.modalContainer.appendChild(modalContent);
    document.body.appendChild(this.modalContainer);

    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';
  }

  private removeModalContainer(): void {
    if (this.modalContainer) {
      document.body.removeChild(this.modalContainer);
      this.modalContainer = null;
      // Restore body scrolling
      document.body.style.overflow = '';
    }
  }

  private showPaymentSuccessMessage(): void {
    this.toastrService.success(AppConstants.EMPTY_STRING, SuccessMessage.BOOKING_SUCCESSFULL);
  }

  private showPaymentFailedMessage(): void {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.PAYMENT_FAILED);
  }

  private showProgressBar(): void {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar(): void {
    this.progressService.isPorgress.next(false);
  }
} 