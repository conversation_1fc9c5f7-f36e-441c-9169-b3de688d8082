<!-- Main -->
<div id="main" class="user-rides">
    
  <!-- Three -->
  <section id="two">
    <h2><strong>
      <svg style="color: green;" width="2em" height="2em" viewBox="0 0 16 16" class="bi bi-check" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.236.236 0 0 1 .02-.022z"/>
    </svg>
    <span id="confirm">Thanks for Booking with us.</span></strong></h2>
    <h3><span class="thanksquote">Your Booking ID is <strong style="font-weight: 900;">{{ bookingId }}</strong>. Please refer to the mail for more details.<br/><br/></span></h3>
    <div class="row">
      <div class="col-12 col-md-6">
                      <div class="mapfield">
                         
                              <div href="#" class="request">
                                  <div class="travel-detail">
                                      <div class="city">Banglore <span class="times">09:05AM</span></div>
                                      <div class="city">Haryana <span class="times">05:30PM</span></div>
                                  </div>
  
                                  <div class="booking-detail">
                                     
                                    <div class="timing"> {{ bookingRequest?.pickUpAddress }}</div>
                                    <div class="timing">  {{ bookingRequest?.dropOffAddress }}</div>
                                  </div>
                                </div>

                                <div class="map">
                                  <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7308976.*********!2d69.37583418232987!3d26.557526419073746!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x396a3efaf7e30e37%3A0xb52b9b4506c088e5!2sRajasthan!5e0!3m2!1sen!2sin!4v1595406213985!5m2!1sen!2sin" width="100%" height="100%" frameborder="0" style="border:0;" allowfullscreen="" aria-hidden="false" tabindex="0"></iframe>
                                </div>
                                

                                <!--<div class="driver-detail">
                                  <span class="rating">2.3 <i class="fa fa fa-star"></i></span>
                                  <div class="user-image">
                                    <img src="{{imageFolderPath}}/driver.jpg">
                                  </div>
                                  <div class="bigheading">Mukesh Ambani
                                    <span class="smallheading">{{ bookingRequest?.carCategory }}</span>
                                    <a href="#" class="report">Report Issue</a>
                                  </div>
      
                                  <div class="bigheading">{{ bookingRequest?.distance }}
                                    <span class="smallheading">kilometers</span>
                                  </div>
      
                                  
                                </div>-->
                              
                      </div>
                      
                      

                  </div>
                  
                  <div class="col-12 col-md-6">
                    <div class="invoice-detail">
                        <div class="fare-head">Your Fare</div>
                        <div class="invoice-price"><i class="fa fa-rupee-sign"></i>{{ bookingRequest?.basicFare + bookingRequest?.gst }}</div>
                        <div class="fare-breakdown">
                            <h2>Fare Breakdown</h2>
                            <div class="fares-row">
                                <span class="rows">Base fare</span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i> {{ bookingRequest?.basicFare }}</span>
                            </div>

                            <!--<div class="fares-row">
                                <span class="rows">Toll, Surcharges and fees <i class="fa fa-question-circle"></i></span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i> 20.20</span>
                            </div>-->

                            <div class="fares-row">
                                <span class="rows">Taxes IGST(5%) </span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i>{{ bookingRequest?.gst }}</span>
                            </div>

                            <div class="fares-row">
                                <span class="rows">Amount to be paid to Driver</span>
                                <span class="fareprice"> <i class="fa fa-rupee-sign"></i>{{ bookingRequest?.basicFare - bookingRequest?.cashAmountToPayDriver }}</span>
                            </div>
                        </div>

                        <div class="payment-by">
                          <div class="payment-type"><i class="fa fa-money-check"></i> Payment</div>
                          <div class="paid"><span class="fa fa-check"></span> Paid by cash</div>
                        </div>

                        <div class="help">
                          <div>Need Help?</div>
                          <small>Tap help in your app to contact us with questions about your trip. For T&C and fare details, visit out website</small>
                        </div>

                        <!--<button type="button" class="download-btn">
                          Download Receipt
                        </button>-->

                    </div>

                </div>


                  
                
    </div>
    
    

  
    
  </section>
          
          <!-- Modal -->
<div class="modal fade" id="myModal" role="dialog">
<div class="modal-dialog">

<!-- Modal content-->
<div class="modal-content">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal">&times;</button>
    <h4 class="modal-title">Modal Header</h4>
  </div>
  <div class="modal-body">
    <p>Some text in the modal.</p>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
  </div>
</div>

</div>
</div>
  

    
  
</div>








