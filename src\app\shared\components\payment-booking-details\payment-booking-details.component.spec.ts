import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrModule } from 'ngx-toastr';
import { PaymentBookingDetailsComponent } from './payment-booking-details.component';
import { BookingRequest } from '../../models/booking.model';

describe('PaymentBookingDetailsComponent', () => {
  let component: PaymentBookingDetailsComponent;
  let fixture: ComponentFixture<PaymentBookingDetailsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PaymentBookingDetailsComponent ],
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        ToastrModule.forRoot()
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentBookingDetailsComponent);
    component = fixture.componentInstance;

    // Initialize component with test data
    component.bookingRequest = new BookingRequest();
    component.bookingRequest.basicFare = 4000;
    component.bookingRequest.fare = 4200;
    component.originalTokenAmount = 800; // 20% of 4000 or minimum 500
    component.bookingRequest.cashAmountToPayDriver = 800;
    component.bookingRequest.paymentOption = 0;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set payment option to partial payment when pay rest to driver is selected', () => {
    // Create mock checkbox elements
    const paypartialCb = document.createElement('input');
    paypartialCb.name = 'paypartialCb';
    paypartialCb.type = 'checkbox';

    const payTotalCb = document.createElement('input');
    payTotalCb.name = 'payTotalCb';
    payTotalCb.type = 'checkbox';
    payTotalCb.checked = true;

    // Call toggleCheckbox with partial payment option
    component.toggleCheckbox(paypartialCb, payTotalCb);

    // Verify the payment option is set correctly
    expect(component.bookingRequest.paymentOption).toBe(1); // Partial payment
    expect(component.amountTobePaid).toBe(800); // Original token amount
    expect(component.bookingRequest.cashAmountToPayDriver).toBe(3200); // 4000 - 800
    expect(paypartialCb.checked).toBe(true);
    expect(payTotalCb.checked).toBe(false);
  });

  it('should set payment option to full payment when pay total is selected', () => {
    // First set to partial payment
    component.bookingRequest.paymentOption = 1;
    component.bookingRequest.cashAmountToPayDriver = 3200;

    // Create mock checkbox elements
    const payTotalCb = document.createElement('input');
    payTotalCb.name = 'payTotalCb';
    payTotalCb.type = 'checkbox';

    const paypartialCb = document.createElement('input');
    paypartialCb.name = 'paypartialCb';
    paypartialCb.type = 'checkbox';
    paypartialCb.checked = true;

    // Call toggleCheckbox with full payment option
    component.toggleCheckbox(payTotalCb, paypartialCb);

    // Verify the payment option is set correctly
    expect(component.bookingRequest.paymentOption).toBe(0); // Full payment
    expect(component.amountTobePaid).toBe(4200); // Full fare
    expect(component.bookingRequest.cashAmountToPayDriver).toBe(0); // No cash to driver
    expect(payTotalCb.checked).toBe(true);
    expect(paypartialCb.checked).toBe(false);
  });
});
