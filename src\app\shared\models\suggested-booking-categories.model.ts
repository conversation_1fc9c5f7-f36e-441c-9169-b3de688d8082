export class SuggestedBookingCategories{
    carCategory: string;
    carCategoryImage: string;
    carCategoryFeatures: string;
    allowedPassengers: string;
    fareAmount: string;
}


export class AddressTokens {
}

export class RichInfo {
}

export class SuggestedLocation {
    type: string;
    typeX: number;
    placeAddress: string;
    latitude: number;
    longitude: number;
    eLoc: string;
    entryLatitude: number;
    entryLongitude: number;
    placeName: string;
    alternateName: string;
    keywords: string[];
    addressTokens: AddressTokens;
    p: number;
    orderIndex: number;
    score: number;
    suggester?: any;
    richInfo: RichInfo;
}

export class suggestedLocations {
    suggestedLocationsAddress: SuggestedLocation[];
    userAddedLocations: any[];
    suggestedSearches: any[];
}
