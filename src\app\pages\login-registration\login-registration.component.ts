import { Component, OnInit } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';
import { finalize } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'src/app/shared/services';
import { LoginRequest } from 'src/app/shared/models/login-request';
import { ProgressService } from 'src/app/shared/services/progress.service';
import { LoggerService } from 'src/app/shared/interceptors/logger.service';

@Component({
  selector: 'app-login-registration',
  templateUrl: './login-registration.component.html',
  styleUrls: ['./login-registration.component.css']
})
export class LoginRegistrationComponent implements OnInit {

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private progresService: ProgressService) {
      this.logger = LoggerService.createLogger('LoginRegistrationComponent');
  }

  private logger: LoggerService;

  busy = false;

  username = '';
  password = '';

  loginError = false;

  imageFolderPath: string = AppConfig.imageFolderPath;

  loginRequest: LoginRequest = {
    email: '', password: ''
  };

  isOTPSend: boolean = false;
  returnUrl: string = '';

  ngOnInit(): void {
    this.logger.trace('ngOnInit() called');
    
    // Get returnUrl from query params and store it for template access
    const queryParams = this.route.snapshot.queryParams;
    this.returnUrl = queryParams['returnUrl'] || '';
    this.logger.trace('returnUrl in login component', this.returnUrl);
    
    this.authService.user$.subscribe((x) => {
      if (this.route.snapshot.url[0].path === 'login') {
        const accessToken = localStorage.getItem('access_token');
        // const refreshToken = localStorage.getItem('refresh_token');
        if (x && accessToken /*&& refreshToken*/) {
          const queryParams = this.route.snapshot.queryParams;
          this.logger.trace('queryParams', queryParams);

          let returnUrl = queryParams['returnUrl'] || '';
          this.logger.trace('returnUrl', returnUrl);
          if (!returnUrl || returnUrl.trim().length === 0) {
            returnUrl = '/userprofile';
            this.logger.trace('modified the returnUrl', returnUrl);
          } else {
            this.logger.trace('No need to modify return url');
          }

          let modifiedQueryParams = ({...queryParams});
          delete modifiedQueryParams['returnUrl'];
          this.logger.trace('modifiedQueryParams', modifiedQueryParams);
          this.router.navigate([returnUrl], { queryParams: modifiedQueryParams });
        }
      } // optional touch-up: if a tab shows login page, then refresh the page to reduce duplicate login
    });
  }

  GetOTP() {
    this.isOTPSend = true;
  }

  login() {
    this.logger.trace('login() called');

    if (!this.username || !this.password) {
      return;
    }
    this.loginRequest = {
      email: this.username, password: this.password
    };

    this.isOTPSend = true;
    this.busy = true;

    const queryParams = this.route.snapshot.queryParams;
    this.logger.trace('queryParams', queryParams);

    let returnUrl = queryParams['returnUrl'] || '';
    this.logger.trace('returnUrl', returnUrl);
    if (!returnUrl || returnUrl.trim().length === 0) {
      returnUrl = '/userprofile';
      this.logger.trace('modified the returnUrl', returnUrl);
    } else {
      this.logger.trace('No need to modify return url');
    }

    let modifiedQueryParams = ({...queryParams});
    delete modifiedQueryParams['returnUrl'];
    this.logger.trace('modifiedQueryParams', modifiedQueryParams);

    this.showProgressBar();
    this.authService
      .login(this.loginRequest)
      .pipe(finalize(() => (this.busy = false)))
      .subscribe(
        (response) => {
          this.hideProgressBar();
          this.router.navigate([returnUrl], { queryParams: modifiedQueryParams });
        },
        (error) => {
          this.hideProgressBar();
          this.loginError = true;
        }
      );
  }

  private showProgressBar = () => {
    this.logger.trace('showProgressBar() called');
    this.progresService.isPorgress.next(true);
  }

  private hideProgressBar = () => {
    this.logger.trace('hideProgressBar() called');
    this.progresService.isPorgress.next(false);
  }
}


