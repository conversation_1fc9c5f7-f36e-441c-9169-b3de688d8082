import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { map } from 'rxjs/operators';
import { LoggerService } from '../../interceptors/logger.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router, 
    private authService: AuthService) {
    this.logger = LoggerService.createLogger('AuthGuard');
  }

  private logger: LoggerService;

  canActivate(next: ActivatedRouteSnapshot):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    return this.authService.user$.pipe(
      map((user) => {
        this.logger.trace('user', user);
        
        const urlSegments = next.url;
        this.logger.trace('urlSegments', urlSegments);
        
        const url = urlSegments[0].path;
        this.logger.trace('url', url);

        const queryParams = next.queryParams;
        this.logger.trace('queryParams', queryParams);

        if (user) {
          return true;
        } else {
          this.router.navigate(
            ['login'], 
            { queryParams: { 
                returnUrl: url , 
                ...queryParams },
              queryParamsHandling: 'merge'},
          );
          return false;
        }
      })
    );
  }
}
