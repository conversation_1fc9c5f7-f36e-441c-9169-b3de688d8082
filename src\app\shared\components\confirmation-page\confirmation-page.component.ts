import { PaymentResponseService } from './../../services/payment-response.service';
import { BookingRequest } from './../../models/booking.model';
import { AppConfig } from 'src/app/configs/app.config';
import { Router } from '@angular/router';
import { LoggerService } from './../../interceptors/logger.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-confirmation-page',
  templateUrl: './confirmation-page.component.html',
  styleUrls: ['./confirmation-page.component.css']
})
export class ConfirmationPageComponent implements OnInit {
  private logger: LoggerService;
  
  bookingId: string;
  bookingRequest: BookingRequest;
  imageFolderPath: string = AppConfig.imageFolderPath;

  constructor(
    private router: Router,
    private paymentResponse: PaymentResponseService,
    private paymentRepsponseService: PaymentResponseService) { 
    this.logger = LoggerService.createLogger('ConfirmationPageComponent');
  }

  ngOnInit(): void {
    this.logger.debug(this.paymentResponse.paymentResponseValue);
    this.bookingId = this.paymentResponse.paymentResponseValue['bookingId'];
    this.bookingRequest = this.paymentRepsponseService.bookingRequestData;
    this.logger.debug(this.bookingRequest);
  }

  navigateToHome = () => {
    this.router.navigate(['home']);
  }
}
