import { CouponsService } from './../../services/coupons.service';
import { PaymentService } from './../../services/payment.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { BookingRequest } from '../../models/booking.model';
import { BookingAPIService } from '../../services/booking-api.service';
import { ToastrService } from 'ngx-toastr';
import { AppConstants } from '../../constants/AppConstants';
import { SuccessMessage, ErrorMessage } from '../../constants/message.content';
import { ProgressService } from '../../services/progress.service';
import { AuthService, ApplicationUser } from '../../services';
import { LoggerService } from '../../interceptors/logger.service';
import { Subscription } from 'rxjs';
import { GenericAPIResponse } from '../../models/generic-api-response.model';
import { BookingDetails } from '../../models/booking-details.model';
import { PaymentResponseService } from '../../services/payment-response.service';

@Component({
  selector: 'app-payment-booking-details',
  templateUrl: './payment-booking-details.component.html',
  styleUrls: ['./payment-booking-details.component.css']
})
export class PaymentBookingDetailsComponent implements OnInit, OnDestroy {

  constructor(
    private bookingAPIService: BookingAPIService, 
    private toastrService: ToastrService,
    private progressService: ProgressService,
    private paymentService: PaymentService,
    private authService: AuthService,
    private couponService: CouponsService,
    private paymentResponseService: PaymentResponseService,
    private route: ActivatedRoute,
    private router: Router) { 
    this.logger = LoggerService.createLogger('PaymentBookingDetailsComponent');
  }

  private logger: LoggerService;
  imageFolderPath: string = AppConfig.imageFolderPath;
  amountTobePaid: number;
  fixedAmount: number;
  discount: number = 0;
  couponCode: string='';
  originalTokenAmount: number; // Store original token amount for payment option switching

  user: ApplicationUser;

  bookingRequest: BookingRequest;

  private queryParamsSubscription: Subscription;
  private bookingGETAPISubscription: Subscription;

  ngOnInit() {
    console.log("ngOnInit called.");
    this.logger.trace('PaymentBookingDetailsComponent:: ngOnInit() called');
    this.resetFields();
    this.initUser();
    this.queryParamsSubscription = this.subsribeQueryParams();
  }

  ngOnDestroy() {
    console.log("ngDestroy called.");
    this.logger.trace('ngOnDestroy() called');
    if (this.queryParamsSubscription) {
      this.queryParamsSubscription.unsubscribe();
      this.logger.trace('queryParamsSubscription unsubscribed')
    }
    if (this.bookingGETAPISubscription) {
      this.bookingGETAPISubscription.unsubscribe();
      this.logger.trace('bookingGETAPISubscription unsubscribed')
    }
  }

  private resetFields = () => {
    this.logger.trace('resetFields() called');
    this.bookingRequest = new BookingRequest();
  }

  private initUser = () => {
    this.logger.trace('initUser() called');
    this.user = new ApplicationUser();
    this.authService.user$.subscribe((user) => {
      this.user = user;
      this.logger.trace('user change to', this.user);
    });
  }

  private subsribeQueryParams = (): Subscription => {
    this.logger.trace('subsribeQueryParams() called');
    return this.route.queryParams
      .subscribe(paramMap => {
        this.logger.trace('listened to value in paramMap', paramMap);
        this.readQueryParams(paramMap);
    });
  }

  private readQueryParams = (params: Params) => {
    this.logger.trace('readQueryParams() called with params', params);
    this.logger.trace('params', params);

    const pickUpAddressLongLat = params[AppConstants.BOOKING_PICKUP_LNG_LAT_QUERY_PARAM];
    this.logger.trace('pickUpAddressLongLat', pickUpAddressLongLat);
    if (!pickUpAddressLongLat || pickUpAddressLongLat.trim().length === 0) {
      this.logger.trace('pickUpAddressLongLat is invalid');
      this.navigateHomePage();
      return;
    }

    const dropOffAddressLongLat = params[AppConstants.BOOKING_DROPOFF_LNG_LAT_QUERY_PARAM];
    this.logger.trace('dropOffAddressLongLat', dropOffAddressLongLat);
    if (!dropOffAddressLongLat || dropOffAddressLongLat.trim().length === 0) {
      this.logger.trace('dropOffAddressLongLat is invalid');
      this.navigateHomePage();
      return;
    }

    const tripType = params[AppConstants.BOOKING_TRIP_TYPE_QUERY_PARAM];
    this.logger.trace('tripType', tripType);
    if (!tripType || tripType.trim().length === 0) {
      this.logger.trace('tripType is invalid');
      this.navigateHomePage();
      return;
    }

    const categoryName = params[AppConstants.BOOKING_CATEGORY_QUERY_PARAM];
    this.logger.trace('categoryName', categoryName);
    if (!categoryName || categoryName.trim().length === 0) {
      this.logger.trace('categoryName is invalid');
      this.navigateHomePage();
      return;
    }

    const from = params[AppConstants.BOOKING_FROM_ADDRESS_QUERY_PARAM];
    this.logger.trace('from', from);
    if (!from || from.trim().length === 0) {
      this.logger.trace('from is invalid');
      this.navigateHomePage();
      return;
    }

    const to = params[AppConstants.BOOKING_TO_ADDRESS_QUERY_PARAM];
    this.logger.trace('to', to);
    if (!to || to.trim().length === 0) {
      this.logger.trace('to is invalid');
      this.navigateHomePage();
      return;
    }

    const pickupcity = params[AppConstants.BOOKING_PICKUP_CITY_QUERY_PARAM];
    const dropoffcity = params[AppConstants.BOOKING_DROPOFF_CITY_QUERY_PARAM];

    this.getBookingDetails(
      pickUpAddressLongLat,
      dropOffAddressLongLat,
      tripType,
      categoryName,
      from,
      to,
      pickupcity,
      dropoffcity);
  }

  private getBookingDetails = (
    pickUpAddressLongLat: string,
    dropOffAddressLongLat: string,
    tripType: string,
    categoryName: string,
    from: string,
    to: string,
    pickupcity: string,
    dropoffcity: string) => {
    this.logger.trace('getBookingDetails() called with pickUpAddressLongLat', pickUpAddressLongLat,
      'dropOffAddressLongLat', dropOffAddressLongLat,
      'tripType', tripType,
      'categoryName', categoryName,
      'from', from,
      'to', to);

    this.showProgressBar();
    this.bookingGETAPISubscription = this.bookingAPIService.getSelectedRouteCategoryFareDetails(
      pickUpAddressLongLat,
      dropOffAddressLongLat,
      tripType,
      categoryName).subscribe(
        // TODO Use model
        (response: GenericAPIResponse<BookingDetails>) => {
          this.hideProgressBar();
          this.parseBookingDetailsResponse(
            response,
            pickUpAddressLongLat,
            dropOffAddressLongLat,
            tripType,
            categoryName,
            from,
            to,
            pickupcity,
            dropoffcity);
        },
        (error) => {
          this.hideProgressBar();
          this.logger.error('getBookingDetails() error', error);
          this.showBookingDetailsFailError();
        }
    );
  }

  private parseBookingDetailsResponse = (
    response: GenericAPIResponse<BookingDetails>,
    pickUpAddressLongLat: string,
    dropOffAddressLongLat: string,
    tripType: string,
    categoryName: string,
    from: string,
    to: string, 
    pickupcity: string,
    dropoffcity: string) => {
    this.logger.trace('parseBookingDetailsResponse() called with response', response,
      'pickUpAddressLongLat', pickUpAddressLongLat,
      'dropOffAddressLongLat', dropOffAddressLongLat,
      'tripType', tripType,
      'categoryName', categoryName,
      'from', from,
      'to', to);

    if (!response.succeeded) {
      this.logger.trace('failed to get booking details')
      this.showBookingDetailsFailError();
      return;
    }

    this.createBookingRequest(
      response.data,
      pickUpAddressLongLat,
      dropOffAddressLongLat,
      tripType,
      categoryName,
      from,
      to,
      pickupcity,
      dropoffcity);
  }

  private showBookingDetailsFailError = () => {
    this.logger.trace('showBookingDetailsFailError() called');
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.BOOKING_DETAIL_FAILED);
  }

  private createBookingRequest = (
    details: BookingDetails,
    pickUpAddressLongLat: string,
    dropOffAddressLongLat: string,
    tripType: string,
    categoryName: string,
    from: string,
    to: string,
    pickupcity: string,
    dropoffcity: string) => {
    this.logger.trace('createBookingRequest() called details', details,
      'pickUpAddressLongLat', pickUpAddressLongLat,
      'dropOffAddressLongLat', dropOffAddressLongLat,
      'tripType', tripType,
      'categoryName', categoryName,
      'from', from,
      'to', to);

    this.bookingRequest = new BookingRequest();

    // TODO need to add amount payable request in the model which API will tell
    // TODO Need to add field is whatsapp number in the request model
    // TODO Need to add field fare for remaining kms in the request model
    // TODO Need to add field remaining kms in the request model
    // TODO Need to add field charge per ksm in the request model
    // TODO Need to add field total charge in request model. Will fill by using GET fare API

    this.bookingRequest.tripType = tripType;
    this.bookingRequest.duration = details.duration;
    this.bookingRequest.distance = +details.distance;
    this.bookingRequest.basicFare = +details.basicFare;
    this.bookingRequest.fixRateNote = details.fixRateNote;
    this.bookingRequest.gst = +details.gstFare;
    // TODO What is the difference between gst and gstFare

    this.bookingRequest.couponCode = this.couponCode;
    this.bookingRequest.couponDiscount = this.discount; // TODO Keep it zero intially and then when code is applied adjust total fare

    if (this.user) {
      this.logger.trace('filling booking details for user', this.user);
      this.bookingRequest.travelerName = this.user.firstName + ' ' + this.user.lastName;
      this.bookingRequest.phoneNumber = this.user.phoneNumber;
      this.bookingRequest.mailId = this.user.email;
    }
    
    this.bookingRequest.paymentMode = 0; // TODO How should this change? Does it depend on razor pay cash etc?
    this.bookingRequest.bookingCreatedBy = "0"; // TODO How should this change? Does it depend on razor pay cash etc?
    this.bookingRequest.razorpayPaymentId = null; // TODO Will handle after razor pay payment integration
    this.bookingRequest.razorpayOrderid = null; // TODO Will handle after razor pay payment integration
    this.bookingRequest.razorpaySignature = null; // TODO Will handle after razor pay payment integration
    this.bookingRequest.razorpayStatus = null; // TODO Will handle after razor pay payment integration

    this.bookingRequest.paymentOption = 0; // 0 = Full payment (default), 1 = Partial payment (pay rest to driver)

    // this.bookingRequest.tollCharge = 450; // TODO Details not sent in API

    this.bookingRequest.fare = +details.fare;
    this.bookingRequest.perKMCharges = details.perKMCharges;
    this.bookingRequest.cashAmountToPayDriver = Math.round((0.2 * this.bookingRequest.basicFare)<500 ? 500 : (0.2 * this.bookingRequest.basicFare));
    this.originalTokenAmount = this.bookingRequest.cashAmountToPayDriver; // Store original token amount

    this.bookingRequest.pickUpCity = pickupcity;
    this.bookingRequest.dropOffCity = dropoffcity;
    this.bookingRequest.carCategory = details.categoryName;
    this.bookingRequest.carFeatures = details.features;
    this.bookingRequest.carImage = details.categoryImage;
    this.bookingRequest.carCapacity = details.capacity;
    this.bookingRequest.pickUpAddress = from;
    this.bookingRequest.dropOffAddress = to;
    // this.bookingRequest.pickUpTime = this.pickupDropoffRequest.bookingTime;
    // this.bookingRequest.pickUpDate = this.pickupDropoffRequest.bookingDate;

    // this.bookingRequest.pickUpAddressLongLat = this.pickupDropoffRequest.pickUpAddressLongituted + ',' + this.pickupDropoffRequest.pickUpCityLatitutde;
    // this.bookingRequest.dropOffAddressLongLat = this.pickupDropoffRequest.dropOffAddressLongituted + ',' + this.pickupDropoffRequest.dropOffCityLatitutde;
    this.amountTobePaid = this.bookingRequest.fare;
    this.fixedAmount = this.bookingRequest.fare;
    this.logger.trace('bookingRequest', this.bookingRequest);
  }

  private navigateHomePage = () => {
    this.logger.trace('navigateHomePage() called');
    this.router.navigate(['/home']);
  }

  validateAndSubmit = () => {
    // TODO Add form validation code
    this.logger.trace('validateAndSubmit() called bookingRequest', this.bookingRequest);
    this.bookingRequest.fare = this.amountTobePaid;
    this.paymentResponseService.bookingRequestData = this.bookingRequest;
    this.paymentService.initPayment();
  }

  toggleCheckbox = (cb1: HTMLInputElement, cb2:HTMLInputElement) => {
    cb1.checked = true;
    cb2.checked = false;
    this.logger.trace(cb1.name);
    if(cb1.name=='paypartialCb'){
      // Pay rest to driver option selected
      this.amountTobePaid = this.originalTokenAmount; // Pay the original token amount
      this.bookingRequest.paymentOption = 1; // 1 = Partial payment (pay rest to driver)
      // Update cashAmountToPayDriver to reflect the remaining amount to be paid to driver
      this.bookingRequest.cashAmountToPayDriver = this.bookingRequest.basicFare - this.originalTokenAmount;
    }else{
      // Full payment option selected
      this.amountTobePaid = this.bookingRequest.fare;
      this.bookingRequest.paymentOption = 0; // 0 = Full payment
      // Reset cashAmountToPayDriver to the original token amount (no cash to driver for full payment)
      this.bookingRequest.cashAmountToPayDriver = 0;
      this.subsribeQueryParams();
    }
  }

  applyCoupon = (couponCode: HTMLInputElement, applyBtn: HTMLInputElement, appliedText: HTMLInputElement) => {

    if(couponCode.value.trim()==''){
      this.showCouponEnteredMessage();
      return;
    }

    this.couponService.applyCoupon(couponCode.value).subscribe(response => { 
      this.logger.trace(response);     
      var discount =  this.couponService.validateCoupon(response['data'],this.bookingRequest.basicFare);
      this.logger.trace(discount); 
      if(discount=='-1'){
        this.showCouponFailedMinFareMessage();
      }else{
        this.showCouponAppliedMessage(couponCode.value, discount);
        this.discount=discount;
        this.couponCode = couponCode.value;
        appliedText.style.display='';
        applyBtn.disabled = true;
        couponCode.disabled = true;
      }
    },
    error => {
      this.logger.error(error);
      this.showCouponNotValidMessage();
    }
    );
  }

  rejectCoupon = (couponCode: HTMLInputElement, applyBtn: HTMLInputElement, appliedText: HTMLInputElement) => {
    this.discount = 0;
    this.couponCode=''; 
    couponCode.disabled = false;
    couponCode.value ='';
    applyBtn.disabled=false;
    appliedText.style.display='none';
  }

  private showBookingSuccessMessage = () => {
    this.toastrService.success(AppConstants.EMPTY_STRING, SuccessMessage.BOOKING_SUCCESSFULL);
  }

  private showCouponFailedMinFareMessage = () => {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.MIN_FARE_COUPON);
  }

  private showCouponNotValidMessage = () => {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.COUPON_NOT_VALID);
  }

  private showCouponEnteredMessage = () => {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.EMPTY_COUPON);
  }

  private showCouponAppliedMessage = (coupon: string, discount: number) => {
    this.toastrService.success(AppConstants.EMPTY_STRING, SuccessMessage.COUPON_APPLIED);
  }

  private showProgressBar = () => {
    this.logger.trace('showProgressBar() called');
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar = () => {
    this.logger.trace('hideProgressBar() called');
    this.progressService.isPorgress.next(false);
  }  
}
